import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Minus, Plus, Trash2, ArrowLeft, Crown, Gift, MessageCircle, Sparkles, Heart } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { Link } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';

const Cart = () => {
  const { items, updateQuantity, removeItem, total, clearCart } = useCart();
  const [showConcierge, setShowConcierge] = useState(false);

  // VIP status calculation
  const isVipEligible = total >= 1000;
  const vipProgress = Math.min((total / 1000) * 100, 100);

  if (items.length === 0) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-background pt-20 silk-texture">
          <div className="container mx-auto px-4 py-16">
            <div className="text-center max-w-lg mx-auto">
              <div className="mb-8">
                <div className="w-24 h-24 mx-auto mb-6 bg-card/50 rounded-full flex items-center justify-center backdrop-blur-sm border border-border/30">
                  <Heart className="h-12 w-12 text-muted-foreground" />
                </div>
                <h1 className="text-4xl font-serif font-bold mb-4 text-flow">Your Heritage Collection</h1>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  Your luxury collection awaits. Begin your journey with elmohr's finest pieces.
                </p>
              </div>

              <Link to="/shop">
                <Button variant="luxury" size="lg" className="silk-thread">
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Discover Collections
                </Button>
              </Link>

              <div className="mt-8 p-6 bg-card/30 backdrop-blur-sm rounded-lg border border-border/30">
                <h3 className="font-serif font-semibold mb-2">Luxury Concierge Service</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Need assistance finding the perfect piece? Our luxury concierge is here to help.
                </p>
                <Button variant="outline" size="sm" className="silk-thread">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Chat with Concierge
                </Button>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Navigation />
      <div className="min-h-screen bg-background pt-20 silk-texture">
        <div className="container mx-auto px-4 py-8">
          {/* Luxury Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 gap-4">
            <div>
              <h1 className="text-4xl font-serif font-bold text-flow mb-2">Heritage Collection</h1>
              <p className="text-muted-foreground">Your curated selection of luxury pieces</p>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/shop">
                <Button variant="outline" className="silk-thread">
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Continue Shopping
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={() => setShowConcierge(!showConcierge)}
                className="silk-thread"
              >
                <MessageCircle className="h-5 w-5 mr-2" />
                Concierge
              </Button>
            </div>
          </div>

          {/* VIP Progress Bar */}
          {!isVipEligible && (
            <div className="mb-8 p-6 bg-gradient-to-r from-primary/10 to-primary-glow/10 rounded-lg border border-primary/20">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-primary" />
                  <span className="font-serif font-semibold">VIP Heritage Member</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  ${(1000 - total).toFixed(2)} away
                </span>
              </div>
              <div className="w-full bg-background/50 rounded-full h-2 mb-2">
                <div
                  className="bg-gradient-to-r from-primary to-primary-glow h-2 rounded-full transition-all duration-500"
                  style={{ width: `${vipProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-muted-foreground">
                Unlock exclusive benefits: Priority access, personal styling, and complimentary services
              </p>
            </div>
          )}

          {/* VIP Status */}
          {isVipEligible && (
            <div className="mb-8 p-6 bg-gradient-to-r from-accent/20 to-primary/20 rounded-lg border border-accent/30 luxury-glow">
              <div className="flex items-center gap-3 mb-2">
                <Crown className="h-6 w-6 text-accent" />
                <span className="font-serif font-bold text-lg">VIP Heritage Member</span>
                <Badge className="bg-accent text-accent-foreground">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Enjoy complimentary services, priority access, and exclusive member benefits
              </p>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Luxury Cart Items */}
            <div className="lg:col-span-2 space-y-6">
              {items.map((item, index) => (
                <Card key={`${item.id}-${item.size}-${item.color}`} className="luxury-card bg-card/50 backdrop-blur-sm border-border/30 silk-float" style={{ animationDelay: `${index * 0.1}s` }}>
                  <CardContent className="p-6">
                    <div className="flex flex-col sm:flex-row gap-6">
                      {/* Premium Product Image */}
                      <div className="w-full sm:w-32 h-32 bg-muted rounded-lg overflow-hidden flex-shrink-0 relative group">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>

                      {/* Enhanced Product Details */}
                      <div className="flex-1 space-y-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-serif font-semibold text-lg mb-1">{item.name}</h3>
                            <div className="flex flex-wrap gap-2 mb-2">
                              {item.size && (
                                <Badge variant="secondary" className="text-xs">
                                  Size: {item.size}
                                </Badge>
                              )}
                              {item.color && (
                                <Badge variant="secondary" className="text-xs">
                                  {item.color}
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Handcrafted luxury piece
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(`${item.id}-${item.size}-${item.color}`)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 silk-thread"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="flex items-center justify-between">
                          {/* Luxury Quantity Controls */}
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium uppercase tracking-wider">Quantity</span>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateQuantity(`${item.id}-${item.size}-${item.color}`, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                                className="silk-thread"
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="w-12 text-center font-serif font-medium text-lg">{item.quantity}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateQuantity(`${item.id}-${item.size}-${item.color}`, item.quantity + 1)}
                                className="silk-thread"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Premium Price Display */}
                          <div className="text-right">
                            <p className="font-serif font-bold text-xl text-primary">
                              ${(item.price * item.quantity).toFixed(2)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              ${item.price} each
                            </p>
                          </div>
                        </div>

                        {/* Craftsmanship Note */}
                        <div className="bg-primary/5 rounded-lg p-3 border border-primary/20">
                          <p className="text-xs text-muted-foreground">
                            ✨ Each piece requires 100+ hours of artisan craftsmanship
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Luxury Actions */}
              <div className="flex flex-col sm:flex-row gap-4 justify-between items-center pt-6 border-t border-border/30">
                <Button
                  variant="outline"
                  onClick={clearCart}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 silk-thread"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Collection
                </Button>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="silk-thread">
                    <Heart className="h-4 w-4 mr-2" />
                    Save for Later
                  </Button>
                  <Button variant="outline" size="sm" className="silk-thread">
                    <Gift className="h-4 w-4 mr-2" />
                    Gift Options
                  </Button>
                </div>
              </div>
            </div>

            {/* Luxury Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-24 luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardHeader className="pb-4">
                  <CardTitle className="font-serif text-xl">Heritage Summary</CardTitle>
                  <div className="w-12 h-px bg-gradient-thread"></div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Order Details */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Subtotal</span>
                      <span className="font-serif font-semibold">${total.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Luxury Shipping</span>
                      <span className="font-serif font-semibold">
                        {total >= 500 ? (
                          <span className="text-green-600">Complimentary</span>
                        ) : (
                          '$25.00'
                        )}
                      </span>
                    </div>
                    {isVipEligible && (
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">VIP Concierge</span>
                        <span className="text-accent font-semibold">Included</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-muted-foreground">Tax</span>
                      <span className="font-serif">${(total * 0.08).toFixed(2)}</span>
                    </div>
                  </div>

                  <Separator className="bg-border/50" />

                  {/* Total */}
                  <div className="flex justify-between items-center">
                    <span className="font-serif font-bold text-lg">Total</span>
                    <span className="font-serif font-bold text-2xl text-primary">
                      ${(total + (total >= 500 ? 0 : 25) + (total * 0.08)).toFixed(2)}
                    </span>
                  </div>

                  {/* Free Shipping Progress */}
                  {total < 500 && (
                    <div className="bg-primary/5 rounded-lg p-4 border border-primary/20">
                      <p className="text-sm text-muted-foreground mb-2">
                        Add <span className="font-semibold text-primary">${(500 - total).toFixed(2)}</span> more for complimentary luxury shipping
                      </p>
                      <div className="w-full bg-background/50 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary to-primary-glow h-2 rounded-full transition-all duration-500"
                          style={{ width: `${Math.min((total / 500) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Checkout Button */}
                  <Link to="/checkout" className="block w-full">
                    <Button variant="luxury" size="lg" className="w-full py-6 text-lg">
                      Proceed to Luxury Checkout
                    </Button>
                  </Link>

                  {/* Payment Options */}
                  <div className="space-y-3">
                    <div className="text-center text-sm text-muted-foreground">
                      Secure payment options
                    </div>
                    <div className="flex justify-center gap-2">
                      <div className="px-3 py-2 bg-background/50 rounded border border-border/30 text-xs">
                        Apple Pay
                      </div>
                      <div className="px-3 py-2 bg-background/50 rounded border border-border/30 text-xs">
                        Google Pay
                      </div>
                      <div className="px-3 py-2 bg-background/50 rounded border border-border/30 text-xs">
                        PayPal
                      </div>
                    </div>
                  </div>

                  {/* Luxury Guarantees */}
                  <div className="space-y-2 text-sm text-muted-foreground bg-card/30 rounded-lg p-4 border border-border/20">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span>Lifetime craftsmanship guarantee</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span>30-day heritage return policy</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span>Complimentary worldwide shipping over $500</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span>Personal concierge service</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Concierge Chat */}
              {showConcierge && (
                <Card className="mt-6 luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                  <CardHeader className="pb-4">
                    <CardTitle className="font-serif text-lg flex items-center">
                      <MessageCircle className="h-5 w-5 mr-2 text-primary" />
                      Luxury Concierge
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Our luxury concierge is available to assist with styling advice,
                        size recommendations, and exclusive access to limited pieces.
                      </p>
                      <Button variant="luxury" size="sm" className="w-full">
                        Start Conversation
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default Cart;