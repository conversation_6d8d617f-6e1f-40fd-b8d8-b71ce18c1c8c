import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Eye, ShoppingBag } from 'lucide-react';
import productScarf from '@/assets/product-scarf.jpg';
import productBag from '@/assets/product-bag.jpg';

const ProductShowcase = () => {
  const featuredProducts = [
    {
      id: 1,
      name: 'Silk Heritage Scarf',
      category: 'Accessories',
      price: '$485',
      originalPrice: '$650',
      image: productScarf,
      badge: 'Limited Edition',
      description: 'Hand-embroidered silk scarf with traditional Arabian calligraphy patterns'
    },
    {
      id: 2,
      name: 'Artisan Leather Handbag',
      category: 'Bags',
      price: '$1,250',
      image: productBag,
      badge: 'Bestseller',
      description: 'Premium leather with Arabian-inspired embossing and silk lining'
    },
    {
      id: 3,
      name: 'Embroidered Kaftan',
      category: 'Clothing',
      price: '$890',
      image: productScarf, // Placeholder
      badge: 'New Arrival',
      description: 'Flowing silk kaftan with intricate gold thread embroidery'
    },
    {
      id: 4,
      name: 'Desert Wind Shawl',
      category: 'Accessories',
      price: '$320',
      image: productBag, // Placeholder
      badge: 'Trending',
      description: 'Lightweight cashmere blend with subtle metallic threading'
    }
  ];

  return (
    <section id="shop" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-flow mb-4">
            Featured Collections
          </h2>
          <div className="divider-ornate w-24 h-px bg-gradient-thread mx-auto mb-6"></div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Each piece in our collection represents the perfect harmony between traditional Arabian craftsmanship 
            and contemporary luxury design.
          </p>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredProducts.map((product) => (
            <Card key={product.id} className="product-card group bg-card border-border overflow-hidden">
              <div className="relative">
                {/* Product Image */}
                <div className="aspect-square overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>

                {/* Product Badge */}
                <Badge className="absolute top-4 left-4 bg-primary text-primary-foreground">
                  {product.badge}
                </Badge>

                {/* Hover Actions */}
                <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <Button size="icon" variant="outline" className="bg-background/80 backdrop-blur-sm silk-thread">
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="outline" className="bg-background/80 backdrop-blur-sm silk-thread">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>

                {/* Quick Add to Cart */}
                <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <Button variant="luxury" className="w-full">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-muted-foreground uppercase tracking-wider">
                        {product.category}
                      </p>
                      <h3 className="font-serif font-semibold text-lg text-foreground">
                        {product.name}
                      </h3>
                    </div>
                    <div className="text-right">
                      <p className="font-serif font-bold text-primary text-lg">
                        {product.price}
                      </p>
                      {product.originalPrice && (
                        <p className="text-sm text-muted-foreground line-through">
                          {product.originalPrice}
                        </p>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {product.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-16">
          <Button variant="luxury">
            Explore All Collections
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;