import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Eye, ShoppingBag, Sparkles, Crown } from 'lucide-react';
import { products, collections } from '@/data/products';
import { Link } from 'react-router-dom';

const ProductShowcase = () => {
  // Get featured products from our luxury collections
  const featuredProducts = [
    products.find(p => p.id === 'mohr-classic-tee'),
    products.find(p => p.id === 'heritage-embroidery-tee'),
    products.find(p => p.id === 'heritage-silk-scarf'),
    products.find(p => p.id === 'heritage-bomber')
  ].filter(Boolean);

  const getBadgeVariant = (product: any) => {
    if (product.isLimited) return { text: 'Limited Edition', icon: Crown, className: 'bg-accent text-accent-foreground' };
    if (product.isNew) return { text: 'New Arrival', icon: Sparkles, className: 'bg-primary text-primary-foreground' };
    if (product.isBestseller) return { text: 'Bestseller', icon: null, className: 'bg-primary-glow text-primary-foreground' };
    return { text: 'Heritage', icon: null, className: 'bg-secondary text-secondary-foreground' };
  };

  return (
    <section id="shop" className="py-20 px-4 sm:px-6 lg:px-8 silk-texture">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="mb-6">
            <span className="text-sm text-primary/80 font-arabic tracking-[0.3em] uppercase">
              مجموعات مختارة
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-flow mb-4">
            Featured Collections
          </h2>
          <div className="divider-ornate w-32 h-px bg-gradient-thread mx-auto mb-6"></div>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Each piece in our collection represents the perfect harmony between traditional Arabian craftsmanship
            and contemporary luxury design. Discover the artistry woven into every thread.
          </p>
          <div className="arabic-ornament mt-6 text-center"></div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredProducts.map((product, index) => {
            const badge = getBadgeVariant(product);
            const IconComponent = badge.icon;

            return (
              <Card key={product.id} className="luxury-card group bg-card/80 backdrop-blur-sm border-border/50 overflow-hidden silk-ripple" style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="relative">
                  {/* Product Image */}
                  <div className="aspect-square overflow-hidden relative">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                    />
                    {/* Silk shimmer overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-primary-glow/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Fabric reveal effect */}
                    <div className="absolute inset-0 fabric-reveal opacity-0 group-hover:opacity-100"></div>
                    {/* Dark overlay for text readability */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>

                  {/* Product Badge */}
                  <Badge className={`absolute top-4 left-4 ${badge.className} silk-thread`}>
                    {IconComponent && <IconComponent className="h-3 w-3 mr-1" />}
                    {badge.text}
                  </Badge>

                  {/* Limited Edition Number */}
                  {product.isLimited && product.limitedEditionNumber && (
                    <div className="absolute top-4 right-4 bg-black/80 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
                      #{product.limitedEditionNumber.toString().padStart(3, '0')}
                    </div>
                  )}

                  {/* Hover Actions */}
                  <div className="absolute top-16 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-4 group-hover:translate-x-0">
                    <Button size="icon" variant="outline" className="bg-background/90 backdrop-blur-sm silk-thread luxury-glow">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Link to={`/product/${product.id}`}>
                      <Button size="icon" variant="outline" className="bg-background/90 backdrop-blur-sm silk-thread luxury-glow">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>

                  {/* Quick Add to Cart */}
                  <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                    <Button variant="luxury" className="w-full silk-thread">
                      <ShoppingBag className="h-4 w-4 mr-2" />
                      Add to Cart
                    </Button>
                  </div>

                  {/* Craftsmanship indicator */}
                  <div className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-700" style={{ transitionDelay: '200ms' }}>
                    <div className="text-xs text-white/90 bg-black/60 px-2 py-1 rounded backdrop-blur-sm">
                      Handcrafted
                    </div>
                  </div>
                </div>

                <CardContent className="p-6 relative">
                  {/* Silk thread decoration */}
                  <div className="absolute top-0 left-6 right-6 h-px bg-gradient-thread opacity-30"></div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <p className="text-xs text-muted-foreground uppercase tracking-[0.2em] mb-1">
                          {product.subcategory || product.category}
                        </p>
                        <h3 className="font-serif font-semibold text-lg text-foreground leading-tight group-hover:text-primary transition-colors duration-300">
                          {product.name}
                        </h3>
                        <p className="text-xs text-primary/60 font-arabic mt-1">
                          {product.nameAr}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-serif font-bold text-primary text-xl">
                          ${product.price}
                        </p>
                        {product.originalPrice && (
                          <p className="text-sm text-muted-foreground line-through">
                            ${product.originalPrice}
                          </p>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {product.description}
                    </p>

                    {/* Materials showcase */}
                    <div className="flex flex-wrap gap-1 mt-3">
                      {product.materials.slice(0, 2).map((material, idx) => (
                        <span key={idx} className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                          {material}
                        </span>
                      ))}
                    </div>

                    {/* Stock indicator */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}
                      </span>
                      {product.isLimited && (
                        <span className="text-accent">
                          Limited: {product.limitedEditionNumber}/{product.totalLimitedEdition}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Collections Preview */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {collections.slice(0, 4).map((collection, index) => (
            <div key={collection.id} className="text-center p-6 bg-card/30 backdrop-blur-sm rounded-lg border border-border/30 silk-float" style={{ animationDelay: `${index * 0.5}s` }}>
              <h4 className="font-serif font-semibold text-lg mb-2">{collection.name}</h4>
              <p className="text-sm text-muted-foreground mb-3">{collection.description}</p>
              <div className="text-xs text-primary">{collection.productIds.length} pieces</div>
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-16">
          <Link to="/shop">
            <Button variant="luxury" className="silk-thread">
              Explore All Collections
            </Button>
          </Link>
          <p className="text-sm text-muted-foreground mt-4">
            Discover the complete elmohr heritage collection
          </p>
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;