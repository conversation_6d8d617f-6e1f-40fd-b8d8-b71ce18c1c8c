import { Button } from '@/components/ui/button';
import { ChevronDown, Play, Volume2, VolumeX } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

const HeroSection = () => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      video.addEventListener('loadeddata', () => setIsVideoLoaded(true));
      // Auto-play video when loaded
      video.play().catch(console.error);
    }
  }, []);

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Cinematic Video Background */}
      <div className="absolute inset-0 z-0">
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          loop
          muted={isMuted}
          playsInline
          poster="/src/assets/hero-bg.jpg"
        >
          <source src="/src/assets/videos/arabian-horses-silk.mp4" type="video/mp4" />
          <source src="/src/assets/videos/arabian-horses-silk.webm" type="video/webm" />
          {/* Fallback background image */}
          <div
            className="absolute inset-0 w-full h-full bg-cover bg-center"
            style={{ backgroundImage: 'url(/src/assets/hero-bg.jpg)' }}
          />
        </video>

        {/* Video Overlay Gradients */}
        <div className="absolute inset-0 bg-gradient-hero opacity-60"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-background/20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-background/30 via-transparent to-background/30"></div>

        {/* Video Controls */}
        <button
          onClick={toggleMute}
          className="absolute top-6 right-6 z-30 p-3 bg-black/20 backdrop-blur-sm rounded-full text-white/80 hover:text-white hover:bg-black/30 transition-all duration-300"
          aria-label={isMuted ? "Unmute video" : "Mute video"}
        >
          {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
        </button>
      </div>

      {/* Silk Thread Animations */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        {/* Flowing silk threads */}
        <div className="thread-weave absolute top-1/4 left-0 w-full h-px bg-gradient-thread opacity-60"></div>
        <div className="thread-weave absolute top-1/2 right-0 w-full h-px bg-gradient-thread opacity-40" style={{ animationDelay: '2s' }}></div>
        <div className="thread-weave absolute top-3/4 left-0 w-full h-px bg-gradient-thread opacity-30" style={{ animationDelay: '4s' }}></div>

        {/* Floating silk particles */}
        <div className="absolute top-1/3 left-1/4 w-1 h-1 bg-primary/60 rounded-full animate-pulse"></div>
        <div className="absolute top-2/3 right-1/3 w-2 h-2 bg-primary-glow/40 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/3 left-1/2 w-1 h-1 bg-accent/50 rounded-full animate-pulse" style={{ animationDelay: '3s' }}></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-20 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        <div className="space-y-10">
          {/* Arabian Horse Symbol */}
          <div className="mb-8">
            <div className="w-16 h-16 mx-auto mb-6 relative">
              <div className="absolute inset-0 bg-primary/20 rounded-full animate-pulse"></div>
              <div className="absolute inset-2 bg-primary rounded-full flex items-center justify-center">
                <span className="text-2xl text-primary-foreground font-bold">🐎</span>
              </div>
            </div>
          </div>

          {/* Animated Logo/Brand Name */}
          <div className="space-y-6">
            <h1 className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-serif font-bold text-flow drop-shadow-2xl">
              ELMOHR
            </h1>
            <div className="divider-ornate w-40 h-px bg-gradient-thread mx-auto opacity-80"></div>
            <p className="text-sm sm:text-base text-primary/80 font-arabic tracking-[0.3em] uppercase">
              المهر • الحرير • التراث
            </p>
          </div>

          {/* Luxury Tagline */}
          <div className="space-y-4">
            <p className="text-xl sm:text-2xl md:text-3xl text-foreground/90 font-serif italic tracking-wider leading-relaxed">
              Heritage Reimagined in Silk & Thread
            </p>
            <p className="text-base sm:text-lg text-foreground/70 font-arabic">
              التراث معاد تصوره في الحرير والخيط
            </p>
          </div>

          {/* Luxury Description */}
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-light">
            Discover exclusive luxury fashion pieces inspired by Arabian heritage,
            where every thread tells a story of timeless craftsmanship and cultural elegance.
          </p>

          {/* Premium CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mt-16">
            <button className="btn-luxury group relative">
              <span className="relative z-10">Shop Heritage</span>
              <div className="absolute inset-0 bg-gradient-to-r from-primary-glow to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button className="px-8 py-4 border-2 border-primary/30 text-foreground hover:border-primary hover:bg-primary/10 transition-all duration-500 font-semibold tracking-wider uppercase backdrop-blur-sm">
              Discover Craftsmanship
            </button>
          </div>

          {/* Luxury Stats */}
          <div className="grid grid-cols-3 gap-8 mt-16 pt-8 border-t border-primary/20">
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-primary">100+</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wider">Artisan Hours</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-primary">3</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wider">Generations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-primary">∞</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wider">Heritage</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <div className="flex flex-col items-center space-y-2 animate-bounce">
          <span className="text-xs text-muted-foreground uppercase tracking-wider">Scroll</span>
          <ChevronDown className="h-6 w-6 text-primary" />
        </div>
      </div>

      {/* Luxury Ambient Elements */}
      <div className="absolute inset-0 z-5 pointer-events-none">
        {/* Silk shimmer effects */}
        <div className="absolute top-1/4 left-10 w-2 h-2 bg-primary rounded-full animate-pulse opacity-60"></div>
        <div className="absolute top-3/4 right-16 w-3 h-3 bg-accent rounded-full animate-pulse opacity-40" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/4 w-1 h-1 bg-primary-glow rounded-full animate-pulse opacity-50" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-primary rounded-full animate-pulse opacity-30" style={{ animationDelay: '3s' }}></div>

        {/* Flowing silk ribbons */}
        <div className="absolute top-0 left-1/3 w-px h-full bg-gradient-to-b from-transparent via-primary/20 to-transparent opacity-30"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-primary-glow/20 to-transparent opacity-20" style={{ animationDelay: '2s' }}></div>
      </div>
    </section>
  );
};

export default HeroSection;