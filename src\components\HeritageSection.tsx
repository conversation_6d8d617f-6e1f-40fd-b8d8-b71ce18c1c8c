import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Crown, Sparkles, Heart } from 'lucide-react';

const HeritageSection = () => {
  const heritageValues = [
    {
      icon: Crown,
      title: 'Royal Craftsmanship',
      description: 'Every piece is meticulously handcrafted by master artisans, carrying forward centuries-old techniques passed down through generations.'
    },
    {
      icon: Sparkles,
      title: 'Arabian Elegance',
      description: 'Our designs draw inspiration from the rich cultural tapestry of Arabian heritage, translating timeless beauty into contemporary luxury.'
    },
    {
      icon: Heart,
      title: 'Passionate Artistry',
      description: 'Each thread, each stitch, each detail reflects our unwavering commitment to excellence and our deep respect for traditional craftsmanship.'
    }
  ];

  return (
    <section id="heritage" className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-background to-muted/30">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-flow mb-4">
            The Elmohr Legacy
          </h2>
          <div className="divider-ornate w-24 h-px bg-gradient-thread mx-auto mb-6"></div>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Born from the mystique of Arabian nights and the grace of desert winds, Elmohr represents 
            the eternal bond between the noble Arabian horse and the artisan's thread—a symphony of 
            heritage woven into every fiber.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Story Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h3 className="text-3xl font-serif font-bold text-foreground">
                Where Tradition Meets Innovation
              </h3>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  In the heart of ancient trade routes, where silk caravans once traversed endless dunes, 
                  our story begins. The Arabian horse, a symbol of nobility and grace, has long been the 
                  inspiration for our artisans who weave tales of heritage into every thread.
                </p>
                <p>
                  Elmohr represents more than fashion—it's a bridge between worlds, connecting the wisdom 
                  of our ancestors with the vision of tomorrow. Each piece in our collection carries the 
                  essence of Arabian poetry, the strength of desert winds, and the elegance of silk threads 
                  dancing in moonlight.
                </p>
                <p>
                  Our master craftsmen, guardians of ancient techniques, ensure that every garment tells 
                  a unique story while honoring the timeless traditions that have defined Arabian luxury 
                  for millennia.
                </p>
              </div>
            </div>

            <Button variant="luxury">
              Discover Our Story
            </Button>
          </div>

          {/* Heritage Values */}
          <div className="space-y-6">
            {heritageValues.map((value, index) => (
              <Card key={index} className="product-card bg-card/50 backdrop-blur-sm border-border/50">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-silk rounded-full flex items-center justify-center">
                        <value.icon className="h-6 w-6 text-primary-foreground" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-serif font-semibold text-lg text-foreground">
                        {value.title}
                      </h4>
                      <p className="text-muted-foreground leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 p-8 bg-card/30 backdrop-blur-sm rounded-lg border border-border/50">
          <h3 className="text-2xl font-serif font-bold text-foreground mb-4">
            Experience the Elmohr Difference
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Join our exclusive community and be the first to discover new collections, 
            behind-the-scenes stories, and special invitations to private events.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="luxury">
              Join Our Legacy
            </Button>
            <Button variant="hero">
              Book a Style Consultation
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeritageSection;