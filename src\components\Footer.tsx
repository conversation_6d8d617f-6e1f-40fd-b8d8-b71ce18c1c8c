import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Instagram, Facebook, Twitter, Youtube, Mail, Phone, MapPin } from 'lucide-react';
import elmohrLogo from '@/assets/elmohr-logo.png';

const Footer = () => {
  const footerSections = [
    {
      title: 'Collections',
      links: [
        'Women\'s Fashion',
        'Men\'s Collection',
        'Accessories',
        'Limited Editions',
        'Heritage Line',
        'Seasonal Collections'
      ]
    },
    {
      title: 'Customer Care',
      links: [
        'Size Guide',
        'Care Instructions',
        'Shipping & Returns',
        'Style Consultation',
        'VIP Membership',
        'Gift Cards'
      ]
    },
    {
      title: 'About Elmohr',
      links: [
        'Our Story',
        'Craftsmanship',
        'Sustainability',
        'Press & Media',
        'Careers',
        'Contact Us'
      ]
    }
  ];

  const socialLinks = [
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Youtube, href: '#', label: 'YouTube' }
  ];

  return (
    <footer className="bg-gradient-to-t from-background to-muted/20 border-t border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Newsletter Section */}
        <div className="py-12 border-b border-border/50">
          <div className="text-center max-w-2xl mx-auto">
            <h3 className="text-2xl font-serif font-bold text-foreground mb-4">
              Join the Elmohr Legacy
            </h3>
            <p className="text-muted-foreground mb-6">
              Subscribe to receive exclusive previews, heritage stories, and early access to our latest collections.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="flex-1 bg-background border-border"
              />
              <Button variant="luxury">
                Subscribe
              </Button>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <img 
                src={elmohrLogo} 
                alt="Elmohr" 
                className="h-12 w-auto mb-6"
              />
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Heritage Reimagined in Silk & Thread. Experience the timeless elegance of Arabian craftsmanship 
                through our exclusive luxury fashion collections.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-3 text-muted-foreground">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span>Dubai Design District, UAE</span>
                </div>
                <div className="flex items-center gap-3 text-muted-foreground">
                  <Phone className="h-4 w-4 text-primary" />
                  <span>+971 4 123 4567</span>
                </div>
                <div className="flex items-center gap-3 text-muted-foreground">
                  <Mail className="h-4 w-4 text-primary" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section, index) => (
              <div key={index}>
                <h4 className="font-serif font-semibold text-foreground mb-4">
                  {section.title}
                </h4>
                <ul className="space-y-2">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href="#"
                        className="text-muted-foreground hover:text-primary transition-colors duration-300 text-sm silk-thread"
                      >
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <Separator className="bg-border/50" />

        {/* Bottom Footer */}
        <div className="py-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="text-sm text-muted-foreground">
              © 2024 Elmohr. All rights reserved. | Privacy Policy | Terms of Service
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  aria-label={social.label}
                  className="text-muted-foreground hover:text-primary transition-colors duration-300 silk-thread"
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>

            {/* Language/Currency Selector */}
            <div className="flex items-center gap-4 text-sm">
              <select className="bg-transparent text-muted-foreground border-none cursor-pointer">
                <option>English</option>
                <option>العربية</option>
              </select>
              <span className="text-border">|</span>
              <select className="bg-transparent text-muted-foreground border-none cursor-pointer">
                <option>USD</option>
                <option>EUR</option>
                <option>AED</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;