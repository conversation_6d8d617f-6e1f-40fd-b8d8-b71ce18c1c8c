# elmohr - Heritage Reimagined in Silk & Thread

<div align="center">
  <h1>🐎 elmohr</h1>
  <p><em>المهر • التراث • الحرفية</em></p>
  <p>Ultra-premium luxury e-commerce platform celebrating Arabian heritage through contemporary fashion</p>
</div>

## ✨ Overview

elmohr is a sophisticated luxury e-commerce website that bridges Arabian heritage with contemporary fashion. Inspired by the majestic Arabian horse, our platform showcases handcrafted pieces that tell stories of timeless craftsmanship and cultural elegance.

### 🎯 Brand Identity

- **Name**: el<PERSON><PERSON> (المهر) - inspired by the Arabian horse
- **Symbol**: Arabian horse integrated with silk thread motifs
- **Colors**: Deep Blood Red (#8B0000), Midnight Black, Ivory
- **Typography**: Luxury serif fonts with Arabic calligraphy elements
- **Aesthetic**: Mysterious | Luxurious | Cultural | Artistic

## 🏗️ Architecture

### Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + Custom Luxury Design System
- **UI Components**: shadcn/ui with custom luxury variants
- **State Management**: React Context + Custom Hooks
- **Routing**: React Router DOM
- **Animations**: CSS3 + Custom Silk-Inspired Effects
- **Icons**: Lucide React
- **Build Tool**: Vite with optimizations

### Project Structure

```
elmohr-silk-threads-shop/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # shadcn/ui base components
│   │   ├── HeroSection.tsx # Cinematic hero with video
│   │   ├── Navigation.tsx  # Luxury navigation
│   │   ├── ProductShowcase.tsx # Premium product display
│   │   └── Footer.tsx      # Heritage footer
│   ├── pages/              # Route components
│   │   ├── Index.tsx       # Homepage
│   │   ├── Shop.tsx        # Product catalog
│   │   ├── ProductDetail.tsx # Luxury product pages
│   │   ├── Cart.tsx        # Premium shopping cart
│   │   ├── Checkout.tsx    # Luxury checkout
│   │   └── About.tsx       # Heritage story
│   ├── data/               # Product data & collections
│   │   └── products.ts     # Luxury product catalog
│   ├── contexts/           # React contexts
│   │   └── CartContext.tsx # Shopping cart state
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions
│   └── assets/             # Images, videos, fonts
├── public/
│   ├── videos/             # Hero background videos
│   └── images/             # Product imagery
├── docs/                   # Documentation
│   └── PERFORMANCE_GUIDE.md # Performance optimization guide
└── README.md
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/elmohr-silk-threads-shop.git
   cd elmohr-silk-threads-shop
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open in browser**
   Navigate to [http://localhost:5173](http://localhost:5173)

### Build for Production

```bash
npm run build
npm run preview  # Preview production build
```

## 🎨 Design System

### Color Palette

```css
/* Primary Colors */
--primary: 0 75% 27%;           /* Deep Blood Red */
--primary-glow: 0 65% 35%;      /* Glowing Red */
--background: 0 0% 3%;          /* Deep Black */
--foreground: 0 0% 95%;         /* Ivory White */

/* Accent Colors */
--accent: 45 85% 55%;           /* Gold */
--secondary: 0 0% 12%;          /* Charcoal */
```

### Typography

- **Primary**: Playfair Display (Luxury Serif)
- **Secondary**: Inter (Clean Sans-serif)
- **Arabic**: Amiri (Traditional Arabic)

### Animations

- **Silk Thread Effects**: Flowing, smooth transitions
- **Luxury Hover States**: Elegant transform animations
- **Cultural Motifs**: Subtle Arabian-inspired movements
- **Performance Optimized**: GPU-accelerated, mobile-friendly

## 🛍️ Features

### 🏠 Homepage
- **Cinematic Hero**: Video background with Arabian horses
- **Silk Animations**: Flowing thread effects throughout
- **Heritage Stats**: Cultural significance metrics
- **Premium CTAs**: Luxury call-to-action buttons

### 🛒 Product Catalog
- **Luxury Collections**:
  - T-Shirts: The Mohr Classic, Heritage Embroidery, Silk Thread Oversized
  - Pants: Tailored, Silk Joggers, Heritage Cargo
  - Shirts: Silk & Cotton Hybrid, Statement Pieces
  - Outerwear: Heritage Bomber, Desert Wind Trench
  - Accessories: Silk Scarves, Leather Goods, Luxury Caps

### 📱 Product Pages
- **8K Image Support**: Ultra-high resolution product imagery
- **Zoom Functionality**: Soft zoom for fabric detail inspection
- **Craftsmanship Notes**: Detailed artisan information
- **Cultural Stories**: Heritage and creation stories
- **Material Breakdown**: Premium material specifications

### 🛍️ Shopping Experience
- **VIP Membership**: Exclusive benefits and early access
- **Luxury Concierge**: Personal styling assistance
- **Premium Checkout**: Apple Pay, Google Pay integration
- **Heritage Packaging**: Luxury presentation

### 📖 Cultural Heritage
- **About Us**: elmohr story and Arabian heritage
- **Master Artisans**: Craftsman profiles and techniques
- **The Journey**: From concept to creation process
- **Cultural Elements**: Arabic calligraphy and motifs
