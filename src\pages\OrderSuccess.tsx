import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Package, Mail, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

const OrderSuccess = () => {
  const orderNumber = `EM${Date.now().toString().slice(-8)}`;

  return (
    <div className="min-h-screen bg-background pt-20">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-3xl font-bold mb-4">Order Placed Successfully!</h1>
          <p className="text-xl text-muted-foreground mb-2">شكراً لطلبك</p>
          <p className="text-muted-foreground mb-8">
            Thank you for your order. We've received your payment and will begin processing your order immediately.
          </p>

          {/* Order Details */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Order Number:</span>
                  <span className="font-bold text-primary">{orderNumber}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="font-medium">Order Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="font-medium">Estimated Delivery:</span>
                  <span>{new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Confirmation Email</h3>
              <p className="text-sm text-muted-foreground">
                You'll receive a confirmation email with order details shortly.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Order Processing</h3>
              <p className="text-sm text-muted-foreground">
                Our artisans will carefully prepare your luxury items.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">Delivery</h3>
              <p className="text-sm text-muted-foreground">
                Your order will be delivered to your doorstep with care.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/shop">
              <Button variant="outline" size="lg">
                Continue Shopping
              </Button>
            </Link>
            
            <Link to="/">
              <Button size="lg">
                <Home className="h-5 w-5 mr-2" />
                Return Home
              </Button>
            </Link>
          </div>

          {/* Contact Info */}
          <Card className="mt-8">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">Need Help?</h3>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>If you have any questions about your order, please contact us:</p>
                <p>Email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
                <p>Phone: <a href="tel:+97141234567" className="text-primary hover:underline">+971 4 123 4567</a></p>
                <p>Order tracking will be available within 24 hours.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccess;