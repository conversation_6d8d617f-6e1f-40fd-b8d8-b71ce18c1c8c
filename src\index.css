@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Elmohr Luxury Design System */
    --background: 0 0% 3%;
    --foreground: 0 0% 95%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 8%;
    --popover-foreground: 0 0% 95%;

    /* Deep blood-red primary inspired by Arabian luxury */
    --primary: 0 75% 27%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 0 65% 35%;

    /* Rich secondary tones */
    --secondary: 0 0% 12%;
    --secondary-foreground: 0 0% 90%;

    /* Muted luxury tones */
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 65%;

    /* Gold accent for premium touches */
    --accent: 45 85% 55%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 12%;
    --ring: 0 75% 27%;

    /* Luxury gradients */
    --gradient-silk: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)), hsl(0 0% 8%));
    --gradient-thread: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.3), transparent);
    --gradient-hero: linear-gradient(180deg, hsl(0 0% 3%), hsl(0 0% 1%));
    
    /* Elegant shadows */
    --shadow-luxury: 0 25px 50px -12px hsl(var(--primary) / 0.4);
    --shadow-silk: 0 10px 40px -10px hsl(var(--primary) / 0.2);
    --shadow-thread: 0 0 30px hsl(var(--primary-glow) / 0.3);

    /* Animation properties */
    --transition-silk: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    --transition-thread: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-serif;
  }
}

@layer components {
  /* Silk thread animation effects */
  .silk-thread {
    @apply relative overflow-hidden;
  }
  
  .silk-thread::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent;
    transform: translateX(-100%);
    transition: var(--transition-silk);
  }
  
  .silk-thread:hover::before {
    transform: translateX(100%);
  }

  /* Arabian calligraphy inspired dividers */
  .divider-ornate {
    @apply relative;
  }
  
  .divider-ornate::after {
    content: '◆';
    @apply absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-primary text-xl;
  }

  /* Luxury button variants */
  .btn-luxury {
    @apply relative px-8 py-4 bg-gradient-to-r from-primary to-primary-glow text-primary-foreground font-semibold tracking-wider uppercase overflow-hidden;
    transition: var(--transition-silk);
    box-shadow: var(--shadow-luxury);
  }
  
  .btn-luxury::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent;
    transform: translateX(-100%) skewX(-25deg);
    transition: var(--transition-thread);
  }
  
  .btn-luxury:hover::before {
    transform: translateX(100%) skewX(-25deg);
  }

  /* Product card hover effects */
  .product-card {
    @apply relative cursor-pointer;
    transition: var(--transition-silk);
  }
  
  .product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-luxury);
  }

  /* Flowing text animation */
  .text-flow {
    @apply bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent;
    background-size: 200% auto;
    animation: shimmer 3s ease-in-out infinite;
  }

  @keyframes shimmer {
    0%, 100% { background-position: 200% center; }
    50% { background-position: -200% center; }
  }

  /* Thread weaving animation */
  @keyframes thread-weave {
    0%, 100% { transform: translateX(-100%); opacity: 0; }
    50% { transform: translateX(0%); opacity: 1; }
  }

  .thread-weave {
    animation: thread-weave 4s ease-in-out infinite;
  }

  /* Silk fabric ripple effect */
  @keyframes silk-ripple {
    0% { transform: scale(1) rotate(0deg); opacity: 0.8; }
    50% { transform: scale(1.05) rotate(1deg); opacity: 1; }
    100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
  }

  .silk-ripple {
    animation: silk-ripple 3s ease-in-out infinite;
  }

  /* Floating silk particles */
  @keyframes silk-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    33% { transform: translateY(-10px) rotate(120deg); opacity: 1; }
    66% { transform: translateY(5px) rotate(240deg); opacity: 0.8; }
  }

  .silk-float {
    animation: silk-float 6s ease-in-out infinite;
  }

  /* Luxury glow pulse */
  @keyframes luxury-glow {
    0%, 100% { box-shadow: 0 0 20px hsl(var(--primary) / 0.3); }
    50% { box-shadow: 0 0 40px hsl(var(--primary) / 0.6), 0 0 60px hsl(var(--primary-glow) / 0.4); }
  }

  .luxury-glow {
    animation: luxury-glow 2s ease-in-out infinite;
  }

  /* Fabric texture reveal */
  @keyframes fabric-reveal {
    0% { background-position: -100% 0; }
    100% { background-position: 100% 0; }
  }

  .fabric-reveal {
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.1), transparent);
    background-size: 200% 100%;
    animation: fabric-reveal 2s ease-in-out infinite;
  }

  /* Embroidery stitch animation */
  @keyframes stitch-draw {
    0% { stroke-dashoffset: 100; opacity: 0; }
    50% { opacity: 1; }
    100% { stroke-dashoffset: 0; opacity: 1; }
  }

  .stitch-draw {
    stroke-dasharray: 100;
    animation: stitch-draw 3s ease-in-out infinite;
  }

  /* Silk thread unravel */
  @keyframes thread-unravel {
    0% { width: 0; opacity: 0; }
    50% { opacity: 1; }
    100% { width: 100%; opacity: 0.8; }
  }

  .thread-unravel {
    animation: thread-unravel 2s ease-out forwards;
  }

  /* Luxury card hover effects */
  .luxury-card {
    @apply relative overflow-hidden cursor-pointer;
    transition: var(--transition-silk);
  }

  .luxury-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary-glow/5;
    opacity: 0;
    transition: var(--transition-silk);
  }

  .luxury-card:hover::before {
    opacity: 1;
  }

  .luxury-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-luxury);
  }

  /* Silk texture background */
  .silk-texture {
    background-image:
      radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, hsl(var(--primary-glow) / 0.1) 0%, transparent 50%);
    background-size: 100px 100px;
    background-position: 0 0, 50px 50px;
  }

  /* Arabian calligraphy inspired decorations */
  .arabic-ornament::before {
    content: '◆ ◇ ◆';
    @apply text-primary/30 text-xs tracking-widest;
  }

  .arabic-ornament::after {
    content: '◆ ◇ ◆';
    @apply text-primary/30 text-xs tracking-widest;
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .silk-ripple {
      animation-duration: 4s; /* Slower animations on mobile for better performance */
    }

    .silk-float {
      animation-duration: 8s;
    }

    .thread-weave {
      animation-duration: 6s;
    }

    .luxury-glow {
      animation: none; /* Disable glow on mobile to save battery */
    }

    /* Reduce motion for mobile users */
    .luxury-card:hover {
      transform: translateY(-4px) scale(1.01); /* Reduced hover effects */
    }

    /* Optimize text flow animation for mobile */
    .text-flow {
      animation-duration: 4s;
    }

    /* Mobile-friendly button sizing */
    .btn-luxury {
      @apply px-6 py-3 text-sm;
    }
  }

  /* Touch-friendly interactions */
  @media (hover: none) and (pointer: coarse) {
    .silk-thread:hover::before {
      transform: translateX(50%); /* Reduced animation for touch devices */
    }

    .product-card:hover {
      transform: translateY(-4px); /* Simplified hover for touch */
    }

    /* Disable complex hover effects on touch devices */
    .luxury-card::before {
      opacity: 0.5; /* Always show subtle overlay on touch devices */
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --primary: 0 100% 50%;
      --background: 0 0% 0%;
      --foreground: 0 0% 100%;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .silk-ripple,
    .silk-float,
    .thread-weave,
    .luxury-glow,
    .text-flow,
    .fabric-reveal,
    .stitch-draw,
    .thread-unravel {
      animation: none;
    }

    .silk-thread::before,
    .btn-luxury::before {
      transition: none;
    }

    .luxury-card,
    .product-card {
      transition: none;
    }
  }

  /* Print styles for luxury documents */
  @media print {
    .silk-texture,
    .luxury-glow,
    .fabric-reveal {
      background: none !important;
      box-shadow: none !important;
    }

    .text-flow {
      background: none !important;
      color: black !important;
    }
  }

  /* Dark mode enhancements */
  @media (prefers-color-scheme: dark) {
    .silk-texture {
      background-image:
        radial-gradient(circle at 25% 25%, hsl(var(--primary) / 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, hsl(var(--primary-glow) / 0.05) 0%, transparent 50%);
    }
  }
}