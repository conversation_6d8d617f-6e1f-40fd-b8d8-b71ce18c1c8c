import React, { useState, useRef, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Heart, Star, ArrowLeft, Minus, Plus, ShoppingCart, ZoomIn, Play, Crown, Sparkles, Award, Eye } from 'lucide-react';
import { products } from '@/data/products';
import { useCart } from '@/contexts/CartContext';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';

const ProductDetail = () => {
  const { id } = useParams();
  const product = products.find(p => p.id === id);
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);
  const { addItem } = useCart();

  // Zoom functionality for high-resolution images
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!imageRef.current || !isZoomed) return;

    const rect = imageRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    setZoomPosition({ x, y });
  };

  const getBadgeInfo = (product: any) => {
    if (product.isLimited) return { text: 'Limited Edition', icon: Crown, className: 'bg-accent text-accent-foreground' };
    if (product.isNew) return { text: 'New Arrival', icon: Sparkles, className: 'bg-primary text-primary-foreground' };
    if (product.isBestseller) return { text: 'Bestseller', icon: Award, className: 'bg-primary-glow text-primary-foreground' };
    return null;
  };

  if (!product) {
    return (
      <>
        <Navigation />
        <div className="min-h-screen bg-background pt-20 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
            <Link to="/shop">
              <Button variant="luxury">Back to Shop</Button>
            </Link>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  const handleAddToCart = () => {
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      size: selectedSize,
      color: selectedColor,
    });
  };

  const relatedProducts = products
    .filter(p => p.category === product.category && p.id !== product.id)
    .slice(0, 4);

  const badgeInfo = getBadgeInfo(product);

  return (
    <>
      <Navigation />
      <div className="min-h-screen bg-background pt-20 silk-texture">
        <div className="container mx-auto px-4 py-8">
          {/* Luxury Breadcrumb */}
          <nav className="flex items-center gap-3 mb-8 text-sm">
            <Link to="/" className="text-muted-foreground hover:text-primary silk-thread transition-colors">Home</Link>
            <span className="text-primary/30">◆</span>
            <Link to="/shop" className="text-muted-foreground hover:text-primary silk-thread transition-colors">Shop</Link>
            <span className="text-primary/30">◆</span>
            <span className="font-medium text-foreground">{product.name}</span>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
            {/* Premium Image Gallery */}
            <div className="space-y-6">
              {/* Main Image with Zoom */}
              <div
                className="relative aspect-square overflow-hidden rounded-lg bg-muted luxury-card group"
                onMouseMove={handleMouseMove}
                onMouseEnter={() => setIsZoomed(true)}
                onMouseLeave={() => setIsZoomed(false)}
              >
                <img
                  ref={imageRef}
                  src={product.images[selectedImageIndex]}
                  alt={product.name}
                  className={`w-full h-full object-cover transition-transform duration-700 ${
                    isZoomed ? 'scale-150' : 'scale-100'
                  }`}
                  style={
                    isZoomed
                      ? {
                          transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
                        }
                      : {}
                  }
                />

                {/* Zoom indicator */}
                <div className="absolute top-4 right-4 bg-black/60 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                  <ZoomIn className="h-4 w-4" />
                </div>

                {/* Fabric detail video overlay */}
                <div className="absolute bottom-4 left-4 bg-black/60 text-white px-3 py-2 rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                  <Play className="h-3 w-3 inline mr-1" />
                  View Fabric Details
                </div>

                {/* Silk shimmer overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary-glow/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>

              {/* Thumbnail Gallery */}
              {product.images.length > 1 && (
                <div className="grid grid-cols-4 gap-3">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`aspect-square rounded-lg overflow-hidden border-2 transition-all duration-300 silk-thread ${
                        selectedImageIndex === index
                          ? 'border-primary shadow-luxury'
                          : 'border-border hover:border-primary/50'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}

              {/* Craftsmanship Preview */}
              <div className="bg-card/50 backdrop-blur-sm rounded-lg p-4 border border-border/30">
                <h4 className="font-serif font-semibold mb-2 flex items-center">
                  <Eye className="h-4 w-4 mr-2 text-primary" />
                  Craftsmanship Details
                </h4>
                <p className="text-sm text-muted-foreground">
                  {product.craftsmanshipNotes}
                </p>
              </div>
            </div>

            {/* Luxury Product Information */}
            <div className="space-y-8">
              {/* Heritage Badge and Title */}
              <div>
                {badgeInfo && (
                  <div className="flex items-center gap-3 mb-4">
                    <Badge className={`${badgeInfo.className} silk-thread`}>
                      {badgeInfo.icon && <badgeInfo.icon className="h-3 w-3 mr-1" />}
                      {badgeInfo.text}
                    </Badge>
                    {product.isLimited && product.limitedEditionNumber && (
                      <span className="text-sm text-muted-foreground">
                        #{product.limitedEditionNumber.toString().padStart(3, '0')} of {product.totalLimitedEdition}
                      </span>
                    )}
                  </div>
                )}

                <h1 className="text-4xl md:text-5xl font-serif font-bold mb-3 text-flow">
                  {product.name}
                </h1>
                <p className="text-xl text-primary/70 font-arabic mb-2">{product.nameAr}</p>
                <p className="text-sm text-muted-foreground uppercase tracking-wider">
                  {product.subcategory || product.category}
                </p>
              </div>

              {/* Luxury Price Display */}
              <div className="bg-card/30 backdrop-blur-sm rounded-lg p-6 border border-border/30">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-4xl font-serif font-bold text-primary">${product.price}</span>
                    {product.originalPrice && (
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-lg text-muted-foreground line-through">
                          ${product.originalPrice}
                        </span>
                        <Badge variant="destructive" className="silk-thread">
                          {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                        </Badge>
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground mb-1">Artisan Hours</div>
                    <div className="text-2xl font-bold text-accent">100+</div>
                  </div>
                </div>
              </div>

              {/* Availability Status */}
              <div className="flex items-center justify-between p-4 bg-card/20 rounded-lg border border-border/20">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${product.stock > 0 ? 'bg-green-500' : 'bg-red-500'} luxury-glow`} />
                  <span className="font-medium">
                    {product.stock > 10 ? 'In Stock' : product.stock > 0 ? `Only ${product.stock} left` : 'Out of stock'}
                  </span>
                </div>
                {product.isVipExclusive && (
                  <Badge className="bg-accent text-accent-foreground">
                    <Crown className="h-3 w-3 mr-1" />
                    VIP Exclusive
                  </Badge>
                )}
              </div>

              {/* Luxury Size Selection */}
              {product.sizes.length > 0 && (
                <div className="space-y-3">
                  <label className="text-sm font-medium uppercase tracking-wider">Size</label>
                  <Select value={selectedSize} onValueChange={setSelectedSize}>
                    <SelectTrigger className="silk-thread">
                      <SelectValue placeholder="Select your size" />
                    </SelectTrigger>
                    <SelectContent>
                      {product.sizes.map((size) => (
                        <SelectItem key={size} value={size} className="silk-thread">
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Luxury Color Selection */}
              {product.colors.length > 0 && (
                <div className="space-y-3">
                  <label className="text-sm font-medium uppercase tracking-wider">Color</label>
                  <Select value={selectedColor} onValueChange={setSelectedColor}>
                    <SelectTrigger className="silk-thread">
                      <SelectValue placeholder="Select your color" />
                    </SelectTrigger>
                    <SelectContent>
                      {product.colors.map((color) => (
                        <SelectItem key={color} value={color} className="silk-thread">
                          {color}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Quantity Selection */}
              <div className="space-y-3">
                <label className="text-sm font-medium uppercase tracking-wider">Quantity</label>
                <div className="flex items-center gap-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                    className="silk-thread"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-16 text-center font-serif text-lg">{quantity}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                    disabled={quantity >= product.stock}
                    className="silk-thread"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Premium Add to Cart */}
              <div className="space-y-4 pt-4">
                <Button
                  onClick={handleAddToCart}
                  disabled={product.stock === 0}
                  variant="luxury"
                  className="w-full py-6 text-lg"
                  size="lg"
                >
                  <ShoppingCart className="h-5 w-5 mr-3" />
                  {product.stock === 0 ? 'Out of Stock' : 'Add to Heritage Collection'}
                </Button>

                <div className="flex gap-3">
                  <Button variant="outline" className="flex-1 silk-thread">
                    <Heart className="h-4 w-4 mr-2" />
                    Add to Wishlist
                  </Button>
                  <Button variant="outline" className="flex-1 silk-thread">
                    <Eye className="h-4 w-4 mr-2" />
                    Virtual Try-On
                  </Button>
                </div>
              </div>

              {/* Premium Materials Showcase */}
              <div className="bg-card/30 backdrop-blur-sm rounded-lg p-6 border border-border/30">
                <h3 className="font-serif font-semibold mb-4 flex items-center">
                  <Sparkles className="h-4 w-4 mr-2 text-primary" />
                  Premium Materials
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  {product.materials.map((material, index) => (
                    <div key={material} className="flex items-center justify-between p-3 bg-background/50 rounded border border-border/20">
                      <span className="font-medium">{material}</span>
                      <div className="w-2 h-2 bg-primary rounded-full luxury-glow"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Artisan Story Preview */}
              {product.artisanStory && (
                <div className="bg-gradient-to-r from-primary/5 to-primary-glow/5 rounded-lg p-6 border border-primary/20">
                  <h4 className="font-serif font-semibold mb-2 text-primary">Artisan Story</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {product.artisanStory}
                  </p>
                </div>
              )}
            </div>
        </div>

          {/* Luxury Product Details Tabs */}
          <Tabs defaultValue="craftsmanship" className="mb-20">
            <TabsList className="grid w-full grid-cols-5 bg-card/50 backdrop-blur-sm">
              <TabsTrigger value="craftsmanship" className="silk-thread">Craftsmanship</TabsTrigger>
              <TabsTrigger value="description" className="silk-thread">Description</TabsTrigger>
              <TabsTrigger value="fabric" className="silk-thread">Fabric Details</TabsTrigger>
              <TabsTrigger value="care" className="silk-thread">Care</TabsTrigger>
              <TabsTrigger value="shipping" className="silk-thread">Shipping</TabsTrigger>
            </TabsList>

            <TabsContent value="craftsmanship" className="mt-8">
              <Card className="luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-serif font-bold mb-2">Master Craftsmanship</h3>
                      <div className="w-16 h-px bg-gradient-thread mx-auto"></div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h4 className="font-serif font-semibold mb-3 text-primary">English</h4>
                        <p className="text-muted-foreground leading-relaxed mb-4">
                          {product.craftsmanshipNotes}
                        </p>
                        {product.artisanStory && (
                          <div className="bg-primary/5 rounded-lg p-4 border border-primary/20">
                            <h5 className="font-medium mb-2 text-primary">Artisan Heritage</h5>
                            <p className="text-sm text-muted-foreground">
                              {product.artisanStory}
                            </p>
                          </div>
                        )}
                      </div>

                      <div>
                        <h4 className="font-serif font-semibold mb-3 text-primary">العربية</h4>
                        <p className="text-muted-foreground leading-relaxed font-arabic mb-4">
                          {product.craftsmanshipNotesAr}
                        </p>
                        {product.artisanStoryAr && (
                          <div className="bg-primary/5 rounded-lg p-4 border border-primary/20">
                            <h5 className="font-medium mb-2 text-primary font-arabic">تراث الحرفي</h5>
                            <p className="text-sm text-muted-foreground font-arabic">
                              {product.artisanStoryAr}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Craftsmanship Stats */}
                    <div className="grid grid-cols-3 gap-6 mt-8 pt-6 border-t border-border/30">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary mb-1">100+</div>
                        <div className="text-sm text-muted-foreground">Artisan Hours</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary mb-1">3</div>
                        <div className="text-sm text-muted-foreground">Generations</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-primary mb-1">1</div>
                        <div className="text-sm text-muted-foreground">Masterpiece</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="description" className="mt-8">
              <Card className="luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="font-serif font-semibold mb-3 text-primary">English</h4>
                      <p className="text-muted-foreground leading-relaxed">
                        {product.description}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-serif font-semibold mb-3 text-primary">العربية</h4>
                      <p className="text-muted-foreground leading-relaxed font-arabic">
                        {product.descriptionAr}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="fabric" className="mt-8">
              <Card className="luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-serif font-bold mb-2">Fabric Excellence</h3>
                      <div className="w-16 h-px bg-gradient-thread mx-auto"></div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h4 className="font-serif font-semibold mb-3 text-primary">Fabric Details</h4>
                        <p className="text-muted-foreground leading-relaxed">
                          {product.fabricDetails}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-serif font-semibold mb-3 text-primary">تفاصيل القماش</h4>
                        <p className="text-muted-foreground leading-relaxed font-arabic">
                          {product.fabricDetailsAr}
                        </p>
                      </div>
                    </div>

                    {/* Material Breakdown */}
                    <div className="bg-primary/5 rounded-lg p-6 border border-primary/20">
                      <h5 className="font-medium mb-4 text-primary">Material Composition</h5>
                      <div className="space-y-3">
                        {product.materials.map((material, index) => (
                          <div key={material} className="flex items-center justify-between">
                            <span className="text-sm">{material}</span>
                            <div className="w-2 h-2 bg-primary rounded-full luxury-glow"></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="care" className="mt-8">
              <Card className="luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="font-serif font-semibold mb-4 text-primary">Care Instructions</h4>
                      <p className="text-muted-foreground leading-relaxed mb-4">
                        {product.careInstructions}
                      </p>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>• Store in provided luxury dust bag</p>
                        <p>• Avoid direct sunlight for extended periods</p>
                        <p>• Handle embroidered areas with extra care</p>
                        <p>• Professional cleaning recommended</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-serif font-semibold mb-4 text-primary font-arabic">تعليمات العناية</h4>
                      <p className="text-muted-foreground leading-relaxed font-arabic">
                        {product.careInstructionsAr}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="shipping" className="mt-8">
              <Card className="luxury-card bg-card/50 backdrop-blur-sm border-border/30">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-serif font-bold mb-2">Luxury Delivery</h3>
                      <div className="w-16 h-px bg-gradient-thread mx-auto"></div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="bg-primary/5 rounded-lg p-4 border border-primary/20">
                          <h5 className="font-medium mb-2 text-primary">Complimentary Services</h5>
                          <ul className="space-y-1 text-sm text-muted-foreground">
                            <li>• Free worldwide shipping on orders over $500</li>
                            <li>• Luxury gift packaging included</li>
                            <li>• White-glove delivery available</li>
                            <li>• Personal styling consultation</li>
                          </ul>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="bg-accent/5 rounded-lg p-4 border border-accent/20">
                          <h5 className="font-medium mb-2 text-accent">Delivery Options</h5>
                          <ul className="space-y-1 text-sm text-muted-foreground">
                            <li>• Standard delivery: 5-7 business days</li>
                            <li>• Express delivery: 2-3 business days</li>
                            <li>• Same-day delivery (select cities)</li>
                            <li>• Appointment scheduling available</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Heritage Collection - Related Products */}
          {relatedProducts.length > 0 && (
            <div className="bg-card/20 backdrop-blur-sm rounded-lg p-8 border border-border/30">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-serif font-bold mb-3">Heritage Collection</h2>
                <div className="w-20 h-px bg-gradient-thread mx-auto mb-4"></div>
                <p className="text-muted-foreground">Discover more pieces from our curated collection</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {relatedProducts.map((relatedProduct, index) => (
                  <Card key={relatedProduct.id} className="luxury-card group overflow-hidden silk-float" style={{ animationDelay: `${index * 0.1}s` }}>
                    <Link to={`/product/${relatedProduct.id}`}>
                      <div className="aspect-square overflow-hidden relative">
                        <img
                          src={relatedProduct.image}
                          alt={relatedProduct.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <div className="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                          <Button variant="luxury" size="sm" className="w-full">
                            View Details
                          </Button>
                        </div>
                      </div>
                    </Link>
                    <CardContent className="p-4">
                      <Link to={`/product/${relatedProduct.id}`}>
                        <h3 className="font-serif font-semibold mb-1 hover:text-primary transition-colors">
                          {relatedProduct.name}
                        </h3>
                      </Link>
                      <p className="text-sm text-muted-foreground mb-2">{relatedProduct.nameAr}</p>
                      <div className="flex items-center justify-between">
                        <p className="font-serif font-bold text-primary">${relatedProduct.price}</p>
                        {relatedProduct.isLimited && (
                          <Badge className="bg-accent text-accent-foreground text-xs">
                            Limited
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link to="/shop">
                  <Button variant="luxury" className="silk-thread">
                    Explore Full Collection
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </>
  );
};

export default ProductDetail;